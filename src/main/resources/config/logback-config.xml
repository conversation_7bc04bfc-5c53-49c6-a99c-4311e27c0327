<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--    指定property属性变量-->
       <!-- <property name="log.path" value="/data/logs/cook"/> -->
      <property name="log.path" value="/Users/<USER>/logs/cook"/>
<!--     <property name="log.path" value="/Users/<USER>/logs/cook"/> -->

    <!-- 日志输出格式
     %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n
     -->
    <!-- 控制台 appender-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{TRACE_ID}] %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <!-- 文件 滚动日志 (all)-->
    <appender name="alllog"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志输出路径、文件名 -->
        <file>${log.path}/cook.log</file>
        <!--日志输出格式-->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{TRACE_ID}] %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!--历史日志归档策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 历史日志： 归档文件名 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/itworker.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!--单个文件的最大大小-->
            <maxFileSize>64MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>15</maxHistory>
        </rollingPolicy>


    </appender>


    <!-- 文件 滚动日志 (仅error)-->
    <appender name="errorlog"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志输出路径、文件名 -->
        <file>${log.path}/cook_error.log</file>
        <!--日志输出格式-->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{TRACE_ID}] %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!--历史日志归档策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 历史日志： 归档文件名 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/itworker_error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!--单个文件的最大大小-->
            <maxFileSize>64MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>15</maxHistory>
        </rollingPolicy>

        <!-- 此日志文档只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <!-- root 级别的配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="alllog" />
        <appender-ref ref="errorlog" />
    </root>

</configuration>
